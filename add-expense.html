<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Expense - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css" />
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>
</head>
<body>
  <div class="app-container">
    <header class="header">
      <div class="container header-content">
        <div class="logo">ExpenseTracker</div>
        <div class="user-greeting">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="user-icon">
            <circle cx="12" cy="8" r="5"></circle>
            <path d="M20 21a8 8 0 1 0-16 0"></path>
          </svg>
          <span>Welcome Back, User!</span>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="card">
        <h2 class="section-title">Add New Expense :</h2>

        <form id="expense-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="desc_main">Primary Description:</label>
                    <input type="text" id="desc_main" placeholder="Main expense description" required>
                </div>
                <div class="form-group">
                    <label for="desc_additional">Additional Description:</label>
                    <input type="text" id="desc_additional" placeholder="Additional details">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="amount_home">Home Currency Amount:</label>
                    <input type="number" id="amount_home" placeholder="e.g., 15.50" step="0.01" min="0">
                </div>
                <div class="form-group">
                    <label for="amount_destination">Destination Currency Amount:</label>
                    <input type="number" id="amount_destination" placeholder="e.g., 17.25" step="0.01" min="0">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="date">Date:</label>
                    <input type="date" id="date" required>
                </div>
                <div class="form-group">
                    <label for="category">Category:</label>
 
                    <select id="category" required>
                        <option value="">Select Category</option>

                        <!-- Accommodation -->
                        <option value="Accommodation-Hotel">Accommodation-Hotel</option>
                        <option value="Accommodation-Hostel">Accommodation-Hostel</option>
                        <option value="Accommodation-Airbnb/Rental">Accommodation-Airbnb/Rental</option>
                        <option value="Accommodation-Camping">Accommodation-Camping</option>
                        <option value="Accommodation-ResortFees">Accommodation-Resort Fees</option>
                        <option value="Accommodation-Other">Accommodation-Other</option>

                        <!-- Education -->
                        <option value="Education-TuitionFees">Education-Tuition Fees</option>
                        <option value="Education-SchoolSupplies">Education-School Supplies</option>
                        <option value="Education-Books/Materials">Education-Books/Materials</option>
                        <option value="Education-Courses/Workshops">Education-Courses/Workshops</option>
                        <option value="Education-StudentLoans">Education-Student Loans</option>
                        <option value="Education-Other">Education-Other</option>

                        <!-- Entertainment -->
                        <option value="Entertainment-Movies/Cinema">Entertainment-Movies/Cinema</option>
                        <option value="Entertainment-Concerts/Gigs">Entertainment-Concerts/Gigs</option>
                        <option value="Entertainment-Theater/Shows">Entertainment-Theater/Shows</option>
                        <option value="Entertainment-SportingEvents">Entertainment-Sporting Events</option>
                        <option value="Entertainment-Museums/Galleries">Entertainment-Museums/Galleries</option>
                        <option value="Entertainment-AmusementParks">Entertainment-Amusement Parks</option>
                        <option value="Entertainment-Nightlife/Bars">Entertainment-Nightlife/Bars</option>
                        <option value="Entertainment-StreamingServices">Entertainment-Streaming Services</option>
                        <option value="Entertainment-Books/Magazines">Entertainment-Books/Magazines</option>
                        <option value="Entertainment-Games/Gaming">Entertainment-Games/Gaming</option>
                        <option value="Entertainment-Hobbies">Entertainment-Hobbies</option>
                        <option value="Entertainment-Other">Entertainment-Other</option>

                        <!-- Financial -->
                        <option value="Financial-BankFees">Financial-Bank Fees</option>
                        <option value="Financial-LoanPayments">Financial-Loan Payments</option>
                        <option value="Financial-CreditCardFees">Financial-Credit Card Fees</option>
                        <option value="Financial-InvestmentFees">Financial-Investment Fees</option>
                        <option value="Financial-Taxes">Financial-Taxes</option>
                        <option value="Financial-AdvisorFees">Financial-Advisor Fees</option>
                        <option value="Financial-Other">Financial-Other</option>

                        <!-- Food -->
                        <option value="Food-Groceries">Food-Groceries</option>
                        <option value="Food-Restaurant/DiningOut">Food-Restaurant/Dining Out</option>
                        <option value="Food-Cafe/CoffeeShops">Food-Cafe/Coffee Shops</option>
                        <option value="Food-Takeaway/Delivery">Food-Takeaway/Delivery</option>
                        <option value="Food-Snacks/Vending">Food-Snacks/Vending</option>
                        <option value="Food-Alcohol">Food-Alcohol</option>
                        <option value="Food-WorkLunches">Food-Work Lunches</option>
                        <option value="Food-Other">Food-Other</option>

                        <!-- Gifts & Donations -->
                        <option value="Gifts-Presents">Gifts-Presents</option>
                        <option value="Gifts-CharityDonations">Gifts-Charity Donations</option>
                        <option value="Gifts-Flowers">Gifts-Flowers</option>
                        <option value="Gifts-Other">Gifts-Other</option>

                        <!-- Health & Wellness -->
                        <option value="Health-Doctor/GP">Health-Doctor/GP</option>
                        <option value="Health-Dentist">Health-Dentist</option>
                        <option value="Health-Optician/Eyecare">Health-Optician/Eyecare</option>
                        <option value="Health-Pharmacy/Medication">Health-Pharmacy/Medication</option>
                        <option value="Health-Hospital/Specialist">Health-Hospital/Specialist</option>
                        <option value="Health-Therapy/Counseling">Health-Therapy/Counseling</option>
                        <option value="Health-Gym/Fitness">Health-Gym/Fitness</option>
                        <option value="Health-SportsActivities">Health-Sports Activities</option>
                        <option value="Health-Vitamins/Supplements">Health-Vitamins/Supplements</option>
                        <option value="Health-InsurancePremiums">Health-Insurance Premiums</option>
                        <option value="Health-Other">Health-Other</option>

                        <!-- Home/Housing -->
                        <option value="Home-Rent">Home-Rent</option>
                        <option value="Home-Mortgage">Home-Mortgage</option>
                        <option value="Home-PropertyTaxes">Home-Property Taxes</option>
                        <option value="Home-HomeInsurance">Home-Home Insurance</option>
                        <option value="Home-Repairs/Maintenance">Home-Repairs/Maintenance</option>
                        <option value="Home-Furniture/Decor">Home-Furniture/Decor</option>
                        <option value="Home-Gardening/Lawn">Home-Gardening/Lawn</option>
                        <option value="Home-CleaningServices">Home-Cleaning Services</option>
                        <option value="Home-HOAFees">Home-HOA Fees</option>
                        <option value="Home-Other">Home-Other</option>

                        <!-- Kids/Children -->
                        <option value="Kids-Childcare/Babysitting">Kids-Childcare/Babysitting</option>
                        <option value="Kids-SchoolFees">Kids-School Fees</option>
                        <option value="Kids-SchoolSupplies">Kids-School Supplies</option>
                        <option value="Kids-Toys/Games">Kids-Toys/Games</option>
                        <option value="Kids-Clothing">Kids-Clothing</option>
                        <option value="Kids-Activities/Sports">Kids-Activities/Sports</option>
                        <option value="Kids-Allowance/PocketMoney">Kids-Allowance/Pocket Money</option>
                        <option value="Kids-Diapers/BabySupplies">Kids-Diapers/Baby Supplies</option>
                        <option value="Kids-Other">Kids-Other</option>

                        <!-- Personal Care -->
                        <option value="PersonalCare-Haircut/Salon">Personal Care-Haircut/Salon</option>
                        <option value="PersonalCare-Toiletries">Personal Care-Toiletries</option>
                        <option value="PersonalCare-Cosmetics/Makeup">Personal Care-Cosmetics/Makeup</option>
                        <option value="PersonalCare-Spa/Massage">Personal Care-Spa/Massage</option>
                        <option value="PersonalCare-DryCleaning/Laundry">Personal Care-Dry Cleaning/Laundry</option>
                        <option value="PersonalCare-Other">Personal Care-Other</option>

                        <!-- Pets -->
                        <option value="Pets-Food">Pets-Food</option>
                        <option value="Pets-Vet/Medical">Pets-Vet/Medical</option>
                        <option value="Pets-Grooming">Pets-Grooming</option>
                        <option value="Pets-Toys/Supplies">Pets-Toys/Supplies</option>
                        <option value="Pets-Boarding/Sitting">Pets-Boarding/Sitting</option>
                        <option value="Pets-Insurance">Pets-Insurance</option>
                        <option value="Pets-Other">Pets-Other</option>

                        <!-- Shopping -->
                        <option value="Shopping-Clothing/Apparel">Shopping-Clothing/Apparel</option>
                        <option value="Shopping-Shoes">Shopping-Shoes</option>
                        <option value="Shopping-Accessories">Shopping-Accessories</option>
                        <option value="Shopping-Electronics">Shopping-Electronics</option>
                        <option value="Shopping-Books/Stationery">Shopping-Books/Stationery</option>
                        <option value="Shopping-HomeGoods">Shopping-Home Goods</option>
                        <option value="Shopping-SportingGoods">Shopping-Sporting Goods</option>
                        <option value="Shopping-Software/Apps">Shopping-Software/Apps</option>
                        <option value="Shopping-OnlinePurchases">Shopping-Online Purchases (General)</option>
                        <option value="Shopping-Other">Shopping-Other</option>

                        <!-- Transport -->
                        <option value="Transport-Flights">Transport-Flights</option>
                        <option value="Transport-Train">Transport-Train</option>
                        <option value="Transport-Bus/Coach">Transport-Bus/Coach</option>
                        <option value="Transport-Taxi/Rideshare">Transport-Taxi/Rideshare</option>
                        <option value="Transport-Ferry/Boat">Transport-Ferry/Boat</option>
                        <option value="Transport-RentalCar">Transport-Rental Car</option>
                        <option value="Transport-Fuel/Petrol/Diesel">Transport-Fuel/Petrol/Diesel</option>
                        <option value="Transport-Parking">Transport-Parking</option>
                        <option value="Transport-Tolls">Transport-Tolls</option>
                        <option value="Transport-PublicTransportPass">Transport-Public Transport Pass</option>
                        <option value="Transport-VehicleMaintenance">Transport-Vehicle Maintenance</option>
                        <option value="Transport-VehicleInsurance">Transport-Vehicle Insurance</option>
                        <option value="Transport-VehicleRegistration/Tax">Transport-Vehicle Registration/Tax</option>
                        <option value="Transport-Bicycle/ScooterRental">Transport-Bicycle/Scooter Rental</option>
                        <option value="Transport-Other">Transport-Other</option>

                        <!-- Travel Specific (beyond transport & accommodation) -->
                        <option value="Travel-Activities/Tours">Travel-Activities/Tours</option>
                        <option value="Travel-Souvenirs">Travel-Souvenirs</option>
                        <option value="Travel-Visas/Permits">Travel-Visas/Permits</option>
                        <option value="Travel-Insurance">Travel-Insurance</option>
                        <option value="Travel-ForeignCurrencyExchange">Travel-Foreign Currency Exchange Fees</option>
                        <option value="Travel-LuggageFees">Travel-Luggage Fees</option>
                        <option value="Travel-AirportServices">Travel-Airport Services (Lounge, Wi-Fi)</option>
                        <option value="Travel-Tips/Gratuities">Travel-Tips/Gratuities</option>
                        <option value="Travel-Other">Travel-Other</option>

                        <!-- Utilities -->
                        <option value="Utilities-Electricity">Utilities-Electricity</option>
                        <option value="Utilities-Gas/HeatingOil">Utilities-Gas/Heating Oil</option>
                        <option value="Utilities-Water">Utilities-Water</option>
                        <option value="Utilities-Internet">Utilities-Internet</option>
                        <option value="Utilities-MobilePhone">Utilities-Mobile Phone</option>
                        <option value="Utilities-LandlinePhone">Utilities-Landline Phone</option>
                        <option value="Utilities-Cable/SatelliteTV">Utilities-Cable/Satellite TV</option>
                        <option value="Utilities-Trash/Recycling">Utilities-Trash/Recycling</option>
                        <option value="Utilities-Other">Utilities-Other</option>

                        <!-- Work/Business -->
                        <option value="Work-OfficeSupplies">Work-Office Supplies</option>
                        <option value="Work-Software/Subscriptions">Work-Software/Subscriptions</option>
                        <option value="Work-ProfessionalDevelopment">Work-Professional Development</option>
                        <option value="Work-ClientMeals/Entertainment">Work-Client Meals/Entertainment</option>
                        <option value="Work-BusinessTravel">Work-Business Travel (if separate from personal)</option>
                        <option value="Work-MembershipFees">Work-Membership Fees (Professional)</option>
                        <option value="Work-Other">Work-Other</option>

                        <!-- Other/Miscellaneous -->
                        <option value="Other-Uncategorized">Other-Uncategorized</option>
                        <option value="Other-CashWithdrawal">Other-Cash Withdrawal (for tracking)</option>
                        <option value="Other-LegalFees">Other-Legal Fees</option>
                        <option value="Other-Postage/Shipping">Other-Postage/Shipping</option>
                        <option value="Other-Miscellaneous">Other-Miscellaneous</option>
                        <!-- Removed "Final" as its purpose was unclear. If it's for something specific, it can be added back with a proper subcategory. -->
                    </select>

                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="payment_method">Payment Method:</label>
                    <select id="payment_method" required>
                        <option value="">Select Payment Method</option>
                        <option value="Cash">Cash</option>
                        <option value="Credit Card">Credit Card</option>
                        <option value="Debit Card">Debit Card</option>
                        <option value="Bank Transfer">Bank Transfer</option>
                        <option value="EFT">EFT</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="location">Location:</label>
                    <input type="text" id="location" placeholder="Where was this expense made?">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex-grow: 2;">
                    <label for="tags">Tags (comma-separated):</label>
                    <input type="text" id="tags" class="tags-input" placeholder="e.g., business, travel, lunch">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group" style="flex-grow: 2;">
                    <label for="expense_goal">Expense Goal:</label>
                    <input type="text" id="expense_goal" placeholder="Purpose or goal of this expense">
                </div>
            </div>
            <button type="submit">Add Expense</button>
        </form>

        <div id="status_area"></div>

        <div class="view-all-container" style="margin-top: 20px;">
            <a href="index.html" class="secondary-button">Back to Home</a>
            <a href="expense-cards.html" class="secondary-button">View All Expenses</a>
        </div>
      </div>
    </main>

    <nav class="bottom-nav">
      <div class="container nav-content">
        <a href="index.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          <span>Home</span>
        </a>
        <a href="userconfig.html" class="nav-item active">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="16"></line>
            <line x1="8" y1="12" x2="16" y2="12"></line>
          </svg>
          <span>Currency Settings</span>
        </a>
        <a href="expense-cards.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="2" y="5" width="20" height="14" rx="2"></rect>
            <line x1="2" y1="10" x2="22" y2="10"></line>
          </svg>
          <span>All Expenses</span>
        </a>
        <a href="settings.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
          <span>Settings</span>
        </a>
      </div>
    </nav>
  </div>

  <script type="py" src="./main.py"></script>
</body>
</html>
