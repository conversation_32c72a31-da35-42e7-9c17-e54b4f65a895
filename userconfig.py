import js
from pyodide.ffi import create_proxy
from pyscript import document
import json
from pyodide.http import pyfetch
import asyncio

# --- Constants ---
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
REGIONAL_PARTNERS_FILE = "support-partners.json"  # Keeping the same filename for backward compatibility

# Global variable to store regional partners data
support_partners = {}

# --- DOM Element References ---
home_currency_select = document.getElementById("home_currency")
destination_currency_select = document.getElementById("destination_currency")
exchange_rate_input = document.getElementById("exchange_rate")
save_button = document.getElementById("save_config")
status_area = document.getElementById("status_area")

# Support partner display elements
home_support_display = document.getElementById("home_support_display")
destination_support_display = document.getElementById("destination_support_display")

# Budget input references - Updated to match expense category groups
budget_accommodation_input = document.getElementById("budget_accommodation")
budget_education_input = document.getElementById("budget_education")
budget_entertainment_input = document.getElementById("budget_entertainment")
budget_financial_input = document.getElementById("budget_financial")
budget_food_input = document.getElementById("budget_food")
budget_gifts_input = document.getElementById("budget_gifts")
budget_health_input = document.getElementById("budget_health")
budget_home_input = document.getElementById("budget_home")
budget_kids_input = document.getElementById("budget_kids")
budget_personalcare_input = document.getElementById("budget_personalcare")
budget_pets_input = document.getElementById("budget_pets")
budget_shopping_input = document.getElementById("budget_shopping")
budget_transport_input = document.getElementById("budget_transport")
budget_travel_input = document.getElementById("budget_travel")
budget_utilities_input = document.getElementById("budget_utilities")
budget_work_input = document.getElementById("budget_work")
budget_other_input = document.getElementById("budget_other")

def show_status(message, is_success=True):
    """Displays a status message to the user."""
    if not status_area:
        print(f"Status Error: Cannot find status area element. Message: {message}")
        js.alert(f"Status: {message}") # Fallback to alert if status area missing
        return

    status_area.innerHTML = ""
    div = document.createElement("div")
    div.textContent = message
    div.classList.add("status-message")
    div.classList.add("success" if is_success else "error")
    status_area.appendChild(div)
    # Clear message after 3 seconds using setTimeout from js module
    clear_proxy = create_proxy(lambda: status_area.removeChild(div) if status_area.contains(div) else None)
    js.setTimeout(clear_proxy, 3000)

def load_config():
    """Loads user configuration from localStorage."""
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            config = json.loads(stored_config)
            print("Loaded user configuration:", config)

            # Set the dropdown values
            if 'home-currency' in config and home_currency_select:
                home_currency_select.value = config['home-currency']

            if 'destination-currency' in config and destination_currency_select:
                destination_currency_select.value = config['destination-currency']

            # Set the exchange rate value (default to 1.00 if not found)
            if 'exchange-rate' in config and exchange_rate_input:
                exchange_rate_input.value = config['exchange-rate']
            else:
                exchange_rate_input.value = "1.00000"

            # Load budget amounts
            if 'budgets' in config:
                budgets = config['budgets']

                # Set budget input values if they exist - Updated for new categories
                if 'Accommodation' in budgets and budget_accommodation_input:
                    budget_accommodation_input.value = budgets['Accommodation']

                if 'Education' in budgets and budget_education_input:
                    budget_education_input.value = budgets['Education']

                if 'Entertainment' in budgets and budget_entertainment_input:
                    budget_entertainment_input.value = budgets['Entertainment']

                if 'Financial' in budgets and budget_financial_input:
                    budget_financial_input.value = budgets['Financial']

                if 'Food' in budgets and budget_food_input:
                    budget_food_input.value = budgets['Food']

                if 'Gifts' in budgets and budget_gifts_input:
                    budget_gifts_input.value = budgets['Gifts']

                if 'Health' in budgets and budget_health_input:
                    budget_health_input.value = budgets['Health']

                if 'Home' in budgets and budget_home_input:
                    budget_home_input.value = budgets['Home']

                if 'Kids' in budgets and budget_kids_input:
                    budget_kids_input.value = budgets['Kids']

                if 'PersonalCare' in budgets and budget_personalcare_input:
                    budget_personalcare_input.value = budgets['PersonalCare']

                if 'Pets' in budgets and budget_pets_input:
                    budget_pets_input.value = budgets['Pets']

                if 'Shopping' in budgets and budget_shopping_input:
                    budget_shopping_input.value = budgets['Shopping']

                if 'Transport' in budgets and budget_transport_input:
                    budget_transport_input.value = budgets['Transport']

                if 'Travel' in budgets and budget_travel_input:
                    budget_travel_input.value = budgets['Travel']

                if 'Utilities' in budgets and budget_utilities_input:
                    budget_utilities_input.value = budgets['Utilities']

                if 'Work' in budgets and budget_work_input:
                    budget_work_input.value = budgets['Work']

                if 'Other' in budgets and budget_other_input:
                    budget_other_input.value = budgets['Other']
        else:
            print("No saved configuration found. Using defaults.")
    except Exception as e:
        print(f"Error loading configuration: {e}")
        show_status("Error loading configuration.", is_success=False)

def save_config(_):
    """Saves user configuration to localStorage."""
    try:
        home_currency = home_currency_select.value
        destination_currency = destination_currency_select.value
        exchange_rate_str = exchange_rate_input.value

        if not home_currency:
            show_status("Please select a Home Currency.", is_success=False)
            return

        # If destination_currency is empty, set it to "None"
        if not destination_currency:
            destination_currency = "None"
            exchange_rate_str = ""  # Clear exchange rate if no destination currency
        else:
            # Only validate exchange rate if destination currency is selected
            if not exchange_rate_str:
                show_status("Please enter an Exchange Rate for the selected Destination Currency.", is_success=False)
                return

            # Validate exchange rate
            try:
                exchange_rate = float(exchange_rate_str)
                if exchange_rate <= 0:
                    show_status("Exchange Rate must be greater than zero.", is_success=False)
                    return
                # Format to 2 decimal places
                exchange_rate_str = f"{exchange_rate:.5f}"
            except ValueError:
                show_status("Please enter a valid Exchange Rate.", is_success=False)
                return

        # Get budget values and validate
        budgets = {}

        # Helper function to get and validate budget values
        def get_budget_value(input_element, category_name):
            if input_element and input_element.value.strip():
                try:
                    budget_value = float(input_element.value)
                    if budget_value < 0:
                        show_status(f"{category_name} budget cannot be negative.", is_success=False)
                        return None
                    # Format to 2 decimal places
                    return f"{budget_value:.2f}"
                except ValueError:
                    show_status(f"Invalid {category_name} budget value.", is_success=False)
                    return None
            return "0.00"  # Default value if empty

        # Get budget values for each category - Updated for new categories
        accommodation_budget = get_budget_value(budget_accommodation_input, "Accommodation")
        if accommodation_budget is None:
            return
        budgets["Accommodation"] = accommodation_budget

        education_budget = get_budget_value(budget_education_input, "Education")
        if education_budget is None:
            return
        budgets["Education"] = education_budget

        entertainment_budget = get_budget_value(budget_entertainment_input, "Entertainment")
        if entertainment_budget is None:
            return
        budgets["Entertainment"] = entertainment_budget

        financial_budget = get_budget_value(budget_financial_input, "Financial")
        if financial_budget is None:
            return
        budgets["Financial"] = financial_budget

        food_budget = get_budget_value(budget_food_input, "Food")
        if food_budget is None:
            return
        budgets["Food"] = food_budget

        gifts_budget = get_budget_value(budget_gifts_input, "Gifts")
        if gifts_budget is None:
            return
        budgets["Gifts"] = gifts_budget

        health_budget = get_budget_value(budget_health_input, "Health")
        if health_budget is None:
            return
        budgets["Health"] = health_budget

        home_budget = get_budget_value(budget_home_input, "Home")
        if home_budget is None:
            return
        budgets["Home"] = home_budget

        kids_budget = get_budget_value(budget_kids_input, "Kids")
        if kids_budget is None:
            return
        budgets["Kids"] = kids_budget

        personalcare_budget = get_budget_value(budget_personalcare_input, "PersonalCare")
        if personalcare_budget is None:
            return
        budgets["PersonalCare"] = personalcare_budget

        pets_budget = get_budget_value(budget_pets_input, "Pets")
        if pets_budget is None:
            return
        budgets["Pets"] = pets_budget

        shopping_budget = get_budget_value(budget_shopping_input, "Shopping")
        if shopping_budget is None:
            return
        budgets["Shopping"] = shopping_budget

        transport_budget = get_budget_value(budget_transport_input, "Transport")
        if transport_budget is None:
            return
        budgets["Transport"] = transport_budget

        travel_budget = get_budget_value(budget_travel_input, "Travel")
        if travel_budget is None:
            return
        budgets["Travel"] = travel_budget

        utilities_budget = get_budget_value(budget_utilities_input, "Utilities")
        if utilities_budget is None:
            return
        budgets["Utilities"] = utilities_budget

        work_budget = get_budget_value(budget_work_input, "Work")
        if work_budget is None:
            return
        budgets["Work"] = work_budget

        other_budget = get_budget_value(budget_other_input, "Other")
        if other_budget is None:
            return
        budgets["Other"] = other_budget

        config = {
            'home-currency': home_currency,
            'destination-currency': destination_currency,
            'exchange-rate': exchange_rate_str,
            'budgets': budgets
        }

        js.localStorage.setItem(USER_CONFIG_KEY, json.dumps(config))

        # Update support partner display
        update_support_partner_display()

        show_status("Configuration saved successfully!", is_success=True)
        print("Saved configuration:", config)
    except Exception as e:
        print(f"Error saving configuration: {e}")
        show_status("Error saving configuration.", is_success=False)

async def load_support_partners():
    """Loads the regional partners data from the JSON file."""
    global support_partners
    try:
        response = await pyfetch(REGIONAL_PARTNERS_FILE)
        if response.status == 200:
            support_partners = await response.json()
            print("Loaded regional partners:", support_partners)
            return True
        else:
            print(f"Error loading regional partners: HTTP {response.status}")
            return False
    except Exception as e:
        print(f"Error loading regional partners: {e}")
        return False

def update_support_partner_display():
    """Updates the regional partner display based on selected currencies."""
    # Update home currency regional partner
    home_currency = home_currency_select.value
    if home_currency and home_currency in support_partners:
        home_partner = support_partners[home_currency]
        if home_support_display:
            home_support_display.textContent = f"{home_partner}"
    else:
        if home_support_display:
            home_support_display.textContent = "Not selected"

    # Update destination currency regional partner
    destination_currency = destination_currency_select.value
    if not destination_currency:
        # Handle empty selection (will be saved as "None")
        if destination_support_display:
            destination_support_display.textContent = "None"
    elif destination_currency == "None":
        # Handle explicit "None" value
        if destination_support_display:
            destination_support_display.textContent = "None"
    elif destination_currency in support_partners:
        # Handle valid currency selection
        destination_partner = support_partners[destination_currency]
        if destination_support_display:
            destination_support_display.textContent = f"{destination_partner}"
    else:
        # Handle unknown currency
        if destination_support_display:
            destination_support_display.textContent = "Not selected"

def on_currency_change(_):
    """Handler for currency selection change events."""
    update_support_partner_display()

def populate_currency_dropdowns():
    """Populates the currency dropdowns with options from regional partners."""
    if not support_partners:
        print("Warning: No regional partners data available.")
        return

    # Clear existing options except the first one (placeholder)
    while home_currency_select.options.length > 1:
        home_currency_select.remove(1)

    while destination_currency_select.options.length > 1:
        destination_currency_select.remove(1)

    # Add "None" option to destination currency dropdown
    none_option = document.createElement("option")
    none_option.value = "None"
    none_option.textContent = "None"
    destination_currency_select.appendChild(none_option)

    # Add options for each currency in support_partners
    for currency in support_partners.keys():
        # Add to home currency dropdown
        home_option = document.createElement("option")
        home_option.value = currency
        home_option.textContent = currency
        home_currency_select.appendChild(home_option)

        # Add to destination currency dropdown
        dest_option = document.createElement("option")
        dest_option.value = currency
        dest_option.textContent = currency
        destination_currency_select.appendChild(dest_option)

    # Set up event listeners for currency changes
    home_currency_proxy = create_proxy(on_currency_change)
    destination_currency_proxy = create_proxy(on_currency_change)

    home_currency_select.addEventListener("change", home_currency_proxy)
    destination_currency_select.addEventListener("change", destination_currency_proxy)

    print("Currency dropdowns populated successfully.")

async def initial_setup():
    """Sets up the page and loads existing configuration."""
    print("Initializing user configuration page...")

    # Load regional partners data first
    success = await load_support_partners()
    if success:
        # Populate currency dropdowns
        populate_currency_dropdowns()

        # Load existing configuration
        load_config()

        # Update regional partner display based on loaded configuration
        update_support_partner_display()
    else:
        show_status("Error loading currency data. Using defaults.", is_success=False)

    # Set up event listeners
    if save_button:
        save_proxy = create_proxy(save_config)
        save_button.addEventListener("click", save_proxy)
    else:
        print("Warning: Save button not found.")

# Run initial setup
asyncio.ensure_future(initial_setup())