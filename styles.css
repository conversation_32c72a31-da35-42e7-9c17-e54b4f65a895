/* Base styles */
:root {
    --primary-blue: #0a2342;
    --secondary-blue: #4a7a9d;
    --accent-green: #2ca58d;
    --text-color: #343a40;
    --light-bg: #f8f9fa;
    --divider-color: #ced4da;
    --error-color: #dc3545;
    --border-radius: 0.375rem;
    --box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
      "Helvetica Neue", sans-serif;
    background-color: var(--light-bg);
    color: var(--text-color);
    line-height: 1.5;
  }

  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  .container {
    width: 100%;
    max-width: 28rem; /* 448px */
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Header */
  .header {
    background-color: var(--primary-blue);
    color: white;
    padding: 1rem 0;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    font-weight: 700;
    font-size: 1.25rem;
  }

  .user-greeting {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--light-bg);
    color: var(--text-color);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
  }

  .user-icon {
    color: var(--secondary-blue);
  }

  /* Main content */
  .main-content {
    flex: 1;
    padding: 1.5rem 0;
    width: 100%;
    max-width: 28rem; /* 448px */
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-color);
  }

  .section-title {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-color);
  }

  .section-subtitle {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
  }

  /* Balance section */
  .balance-section {
    margin-bottom: 1.5rem;
  }

  .balance-amount {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.25rem;
  }

  .balance-change {
    color: var(--accent-green);
    font-size: 0.875rem;
  }

  /* Card */
  .card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Quick actions */
  .quick-actions-section {
    margin-bottom: 1.5rem;
  }

  .quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .action-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 5rem;
    padding: 0.5rem;
    border: 1px solid var(--secondary-blue);
    border-radius: var(--border-radius);
    background-color: transparent;
    color: var(--secondary-blue);
    cursor: pointer;
    text-decoration: none;
    font-size: 0.875rem;
    transition: background-color 0.2s;
    text-align: center; /* Ensure text is centered */
  }

  .action-button:hover {
    background-color: rgba(74, 122, 157, 0.1);
  }

  .action-button svg {
    margin-bottom: 0.5rem;
  }

  /* Transactions */
  .transactions-section {
    margin-bottom: 1.5rem;
  }

  /* Expenses List Section */
  .expenses-list-section {
    margin-bottom: 2rem;
    background-color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
  }

  .expenses-list-section .section-title {
    margin-bottom: 1rem;
  }

  .transactions-list {
    padding: 0; /* Remove padding if items have their own */
  }

  .transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--divider-color);
  }

  .transaction-item:last-child {
    border-bottom: none;
  }

  .transaction-title {
    font-weight: 500;
  }

  .transaction-date {
    font-size: 0.875rem;
    color: #6c757d;
  }

  .transaction-amount {
    font-weight: 500;
  }

  .transaction-amount.positive {
    color: var(--accent-green);
  }

  /* Expense Cards */
  .expense-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .expense-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  .card-icon {
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
  }

  .card-details {
    flex: 1;
  }

  .card-details p {
    margin: 0.5rem 0;
    font-size: 0.9rem;
  }

  .card-details strong {
    color: var(--primary-blue);
  }

  .card-edit-button, .card-delete-button {
    background-color: var(--secondary-blue);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.25rem 0.5rem;
    margin-right: 0.5rem;
    cursor: pointer;
    font-size: 0.8rem;
  }

  .card-delete-button {
    background-color: var(--error-color);
  }

  .view-all-container {
    text-align: center;
    margin-top: 1rem;
  }

  /* Buttons */
  .primary-button {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--primary-blue);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    text-decoration: none; /* Added for consistency if used as <a> */
    text-align: center; /* Added for consistency */
  }

  .primary-button:hover {
    background-color: rgba(10, 35, 66, 0.9);
  }

  .primary-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  .secondary-button {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: transparent;
    color: var(--secondary-blue);
    border: 1px solid var(--secondary-blue);
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    text-decoration: none; /* Added for consistency if used as <a> */
  }

  .secondary-button:hover {
    background-color: rgba(74, 122, 157, 0.1);
  }

  /* Bottom navigation */
  .bottom-nav {
    background-color: var(--primary-blue);
    padding: 0.75rem 0;
    position: sticky;
    bottom: 0;
    z-index: 10; /* Ensure it stays on top */
  }

  .nav-content {
    display: flex;
    justify-content: space-around;
  }

  .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: rgba(255, 255, 255, 0.7); /* Slightly dimmed default color */
    text-decoration: none;
    font-size: 0.75rem;
    padding: 0.25rem; /* Add some padding for easier clicking */
    transition: color 0.2s;
  }

  .nav-item:hover {
      color: rgba(255, 255, 255, 0.9);
  }

  .nav-item svg {
    margin-bottom: 0.25rem;
    width: 1.5rem;
    height: 1.5rem;
  }

  .nav-item.active {
    color: white;
    font-weight: 500;
  }

  /* Form styles */
  .expense-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  /* Form row and group styling from example_styles.css */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
  }

  .form-group {
    flex: 1 1 60px;
    min-width: 180px;
    display: flex;
    flex-direction: column;
  }

  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--text-color);
  }

  input[type="text"],
  input[type="number"],
  input[type="date"],
  select,
  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: var(--border-radius);
    box-sizing: border-box;
    font-size: 1rem;
    line-height: 1.5;
    transition: border-color 0.2s, box-shadow 0.2s;
    background-color: #fff;
  }

  /* Style placeholder text */
  input::placeholder,
  .form-input::placeholder,
  .form-textarea::placeholder {
    color: #6c757d;
    opacity: 1; /* Firefox */
  }

  input:focus,
  select:focus,
  .form-input:focus,
  .form-select:focus,
  .form-textarea:focus {
    outline: none;
    border-color: var(--secondary-blue);
    box-shadow: 0 0 0 2px rgba(74, 122, 157, 0.2);
  }

  .form-textarea {
    resize: vertical; /* Allow vertical resize */
    min-height: 5rem;
  }

  /* Form button styling */
  button[type="submit"] {
    background-color: var(--primary-blue);
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1em;
    margin-right: 10px;
    transition: background-color 0.2s ease;
    width: 100%;
  }

  button[type="submit"]:hover {
    background-color: rgba(10, 35, 66, 0.9);
  }

  .form-description {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
  }

  .input-with-icon {
    position: relative;
    display: flex; /* Align icon and input */
    align-items: center;
  }

  .input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color);
    pointer-events: none; /* Ensure icon doesn't block input focus */
  }

  .input-with-icon .form-input {
    padding-left: 2rem; /* Make space for the icon */
    font-size: 1.125rem;
    font-weight: 500;
  }

  /* Status message styling */
  .status-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: var(--border-radius);
    text-align: center;
  }

  .status-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }

  .status-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }

  .error-message {
    color: var(--error-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    min-height: 1rem; /* Reserve space to prevent layout shifts */
  }

  /* Regional Partner Styles */
  .support-partner-container {
    width: 100%;
    margin-top: 1rem;
    padding: 1rem;
    background-color: rgba(74, 122, 157, 0.1);
    border-radius: var(--border-radius);
  }

  .support-partner-container h4 {
    margin-bottom: 0.5rem;
    color: var(--secondary-blue);
  }

  .support-partner-display {
    margin-top: 0.75rem;
  }

  .support-partner-item {
    margin-bottom: 0.5rem;
    display: flex;
    flex-direction: column;
  }

  .support-partner-item strong {
    margin-bottom: 0.25rem;
  }

  .support-partner-item span {
    padding: 0.25rem 0.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--divider-color);
  }

  .support-partner-info {
    font-size: 0.75rem;
    color: var(--secondary-blue);
    margin-top: 0.25rem;
  }

  /* Settings Page Styles */
  .settings-menu {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .settings-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background-color: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--divider-color);
    transition: all 0.2s;
    text-decoration: none;
    color: var(--text-color);
  }

  .settings-item:hover {
    box-shadow: var(--box-shadow);
    border-color: var(--secondary-blue);
  }

  .settings-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background-color: rgba(74, 122, 157, 0.1);
    border-radius: 50%;
    margin-right: 1rem;
    color: var(--secondary-blue);
  }

  .settings-content {
    flex: 1;
  }

  .settings-content h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
  }

  .settings-content p {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
  }

  .settings-arrow {
    color: var(--divider-color);
  }

  .danger-zone .settings-icon {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  /* Report Page Styles */
  .report-summary {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .report-summary p {
    margin: 0;
    font-size: 0.9rem;
  }

  .report-summary span {
    font-weight: 600;
    color: var(--primary-blue);
  }

  .report-cards-section {
    margin-bottom: 1.5rem;
  }

  .report-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
  }

  .report-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    border: 1px solid var(--divider-color);
    transition: transform 0.2s, box-shadow 0.2s;
  }

  .report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  }

  .report-card h3 {
    margin: 0 0 1rem 0;
    color: var(--primary-blue);
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid var(--divider-color);
    padding-bottom: 0.5rem;
  }

  .report-card .subcategory-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(206, 212, 218, 0.3);
  }

  .report-card .subcategory-item:last-of-type {
    border-bottom: none;
    margin-bottom: 1rem;
  }

  .report-card .subcategory-label {
    color: var(--text-color);
    font-size: 0.9rem;
  }

  .report-card .subcategory-value {
    font-weight: 600;
    color: var(--secondary-blue);
    font-size: 0.9rem;
  }

  .report-card .category-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0 0 0;
    border-top: 2px solid var(--divider-color);
    margin-top: 1rem;
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
  }

  .grand-totals-section {
    margin-top: 2rem;
  }

  .grand-totals-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--divider-color);
    font-size: 1rem;
  }

  .grand-totals-item:last-child {
    border-bottom: none;
  }

  .grand-totals-currency {
    color: var(--text-color);
    font-weight: 500;
  }

  .grand-totals-amount {
    font-weight: 700;
    color: var(--primary-blue);
    font-size: 1.1rem;
  }

  /* Data Management Page Styles */
  .data-management-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .data-action-card {
    display: flex;
    padding: 1.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--divider-color);
    box-shadow: var(--box-shadow);
  }

  .data-action-icon {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    margin-right: 1.5rem;
    color: var(--secondary-blue);
  }

  .data-action-content {
    flex: 1;
  }

  .data-action-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-blue);
  }

  .data-action-content p {
    margin: 0 0 1rem 0;
    color: #6c757d;
  }

  .data-format-info {
    margin-top: 1rem;
    padding: 1.5rem;
    background-color: rgba(74, 122, 157, 0.05);
    border-radius: var(--border-radius);
  }

  .data-format-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-blue);
  }

  .data-format-info p {
    margin: 0 0 0.5rem 0;
  }

  .data-format-info ul {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem 1.5rem;
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
  }

  .data-format-info li {
    width: calc(33.333% - 1rem);
    font-size: 0.9rem;
  }

  @media (max-width: 768px) {
    .data-format-info li {
      width: calc(50% - 0.75rem);
    }
  }

  @media (max-width: 480px) {
    .data-format-info li {
      width: 100%;
    }
  }

  /* Responsive form adjustments */
  @media (max-width: 768px) {
    .form-row {
      flex-direction: column;
      gap: 0;
      margin-bottom: 5px;
    }

    .form-group {
      margin-bottom: 8px;
      width: 100%;
    }

    label {
      margin-bottom: 3px;
    }

    input[type="text"],
    input[type="number"],
    input[type="date"],
    select {
      width: 100%;
      margin-bottom: 8px;
      padding: 8px;
    }

    button[type="submit"] {
      width: 100%;
      margin-bottom: 10px;
      margin-right: 0;
    }
  }

  /* Table Styles */
  .table-container {
    width: 100%;
    overflow-x: auto;
    position: relative;
    margin-bottom: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    background-color: white;
    /* Add scrollbar styling for WebKit browsers (Chrome, Safari, newer Edge) */
    scrollbar-width: thin;
    scrollbar-color: var(--secondary-blue) #f1f1f1;
  }

  /* Custom scrollbar styling */
  .table-container::-webkit-scrollbar {
    height: 8px;
  }

  .table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb {
    background: var(--secondary-blue);
    border-radius: 4px;
  }

  .table-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-blue);
  }

  /* Add a visual indicator for scrolling on small screens */
  @media (max-width: 768px) {
    .table-container::after {
      content: "← Scroll →";
      position: absolute;
      bottom: -20px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 0.75rem;
      color: var(--secondary-blue);
      background-color: white;
      padding: 2px 8px;
      border-radius: 12px;
      border: 1px solid var(--divider-color);
      opacity: 0.8;
      pointer-events: none;
      animation: fadeOut 3s forwards 5s;
    }

    @keyframes fadeOut {
      from { opacity: 0.8; }
      to { opacity: 0; }
    }
  }

  /* Add shadows to indicate scrollable content */
  .table-container.shadow-left::before,
  .table-container.shadow-right::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    width: 15px;
    pointer-events: none;
    z-index: 2;
  }

  .table-container.shadow-left::before {
    left: 0;
    background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0));
  }

  .table-container.shadow-right::after {
    right: 0;
    background: linear-gradient(to left, rgba(0,0,0,0.1), rgba(0,0,0,0));
  }

  #expense-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.9rem;
    min-width: 800px; /* Ensures table will scroll on small screens */
    border: 1px solid var(--divider-color);
    border-radius: var(--border-radius);
    overflow: hidden; /* Ensures the border-radius works with table */
  }

  #expense-table th {
    background-color: var(--primary-blue);
    color: white;
    font-weight: 500;
    text-align: left;
    padding: 0.75rem 1rem;
    position: sticky;
    top: 0;
    z-index: 1;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
  }

  #expense-table th:last-child {
    border-right: none;
  }

  #expense-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--divider-color);
    border-right: 1px solid var(--divider-color);
    vertical-align: middle;
  }

  #expense-table td:last-child {
    border-right: none;
  }

  #expense-table tbody tr:last-child td {
    border-bottom: none;
  }

  /* Alternating row colors */
  #expense-table tbody tr:nth-child(even) {
    background-color: rgba(74, 122, 157, 0.05);
  }

  #expense-table tbody tr:hover {
    background-color: rgba(74, 122, 157, 0.1);
  }

  /* Style for delete button in table */
  .delete-button {
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.2s;
  }

  .delete-button:hover {
    background-color: #c82333;
  }

  /* Expandable cell for long text */
  .expandable-cell {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
  }

  .expandable-cell:hover::after {
    content: attr(data-full-text);
    position: absolute;
    left: 0;
    top: 100%;
    background-color: white;
    padding: 0.5rem;
    border: 1px solid var(--divider-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 10;
    white-space: normal;
    max-width: 300px;
    word-wrap: break-word;
  }

  /* Partner info images */
  .image-display-section {
  margin-top: 40px;
  padding: 20px;
  background-color: #f9f9f9;
  text-align: center;
}

.featured-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

  /* Responsive */
  @media (min-width: 640px) {
    .container {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 768px) {
    .quick-actions-grid {
      grid-template-columns: repeat(4, 1fr);
    }

    .main-content {
      max-width: 48rem; /* Increase max-width for larger screens */
    }
  }

  @media (min-width: 1024px) {
    .main-content {
      max-width: 64rem; /* Further increase max-width for even larger screens */
    }
  }
