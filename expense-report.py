import json
import js
from pyodide.http import pyfetch
from pyscript import document

# --- Constants (matching the main app) ---
EXPENSES_STORAGE_KEY = "pyscript_expense_tracker_data"
USER_CONFIG_KEY = "pyscript_expense_tracker_config"
EXPENSE_CATEGORIES_FILE = "expense_category_list.json"

# --- Local Variables for Report Generation ---
report_expenses = []
report_userconfig = {}
report_expense_categories = {}

# --- Helper Functions ---

def get_category_group(category):
    """Extract the category group from a category string (e.g., 'Food-Groceries' -> 'Food')."""
    if '-' in category:
        return category.split('-')[0]
    return category

async def load_expense_categories():
    """Load expense categories from JSON file."""
    global report_expense_categories
    try:
        response = await pyfetch(EXPENSE_CATEGORIES_FILE)
        if response.status == 200:
            report_expense_categories = await response.json()
            print(f"Loaded {len(report_expense_categories)} expense category groups")
        else:
            print(f"Error loading expense categories: Status {response.status}")
    except Exception as e:
        print(f"Error loading expense categories: {e}")

def load_user_config():
    """Load user configuration from localStorage."""
    global report_userconfig
    try:
        stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
        if stored_config:
            report_userconfig = json.loads(stored_config)
            print(f"Loaded user config: Home={report_userconfig.get('home-currency', 'USD')}, Dest={report_userconfig.get('destination-currency', 'USD')}")

            # Ensure budgets are loaded
            if 'budgets' not in report_userconfig:
                report_userconfig['budgets'] = {
                    'Accommodation': "0.00",
                    'Education': "0.00",
                    'Entertainment': "0.00",
                    'Financial': "0.00",
                    'Food': "0.00",
                    'Gifts': "0.00",
                    'Health': "0.00",
                    'Home': "0.00",
                    'Kids': "0.00",
                    'PersonalCare': "0.00",
                    'Pets': "0.00",
                    'Shopping': "0.00",
                    'Transport': "0.00",
                    'Travel': "0.00",
                    'Utilities': "0.00",
                    'Work': "0.00",
                    'Other': "0.00"
                }
        else:
            print("No user config found, using defaults")
            report_userconfig = {
                'home-currency': "USD",
                'destination-currency': "USD",
                'exchange-rate': "1.000000",
                'budgets': {
                    'Accommodation': "0.00",
                    'Education': "0.00",
                    'Entertainment': "0.00",
                    'Financial': "0.00",
                    'Food': "0.00",
                    'Gifts': "0.00",
                    'Health': "0.00",
                    'Home': "0.00",
                    'Kids': "0.00",
                    'PersonalCare': "0.00",
                    'Pets': "0.00",
                    'Shopping': "0.00",
                    'Transport': "0.00",
                    'Travel': "0.00",
                    'Utilities': "0.00",
                    'Work': "0.00",
                    'Other': "0.00"
                }
            }
    except Exception as e:
        print(f"Error loading user config: {e}")
        report_userconfig = {
            'home-currency': "USD",
            'destination-currency': "USD",
            'exchange-rate': "1.000000",
            'budgets': {
                'Accommodation': "0.00",
                'Education': "0.00",
                'Entertainment': "0.00",
                'Financial': "0.00",
                'Food': "0.00",
                'Gifts': "0.00",
                'Health': "0.00",
                'Home': "0.00",
                'Kids': "0.00",
                'PersonalCare': "0.00",
                'Pets': "0.00",
                'Shopping': "0.00",
                'Transport': "0.00",
                'Travel': "0.00",
                'Utilities': "0.00",
                'Work': "0.00",
                'Other': "0.00"
            }
        }

def load_expenses():
    """Load expenses from localStorage."""
    global report_expenses
    try:
        stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
        if stored_data:
            print("Found saved data in localStorage. Loading...")
            loaded_list = json.loads(stored_data)
            temp_expenses = []
            for i, item in enumerate(loaded_list):
                try:
                    # Handle both new and old variable names for backward compatibility
                    desc_main = item.get('description_main', item.get('description_primary', ''))
                    desc_additional = item.get('description_additional', item.get('description_secondary', ''))

                    # Create expense object with consistent field names
                    expense_obj = {
                        'id': item.get('exp_id', i + 1),
                        'desc_main': desc_main,
                        'desc_additional': desc_additional,
                        'description': f"{desc_main} - {desc_additional}" if desc_additional else desc_main,
                        'amount_home': float(item.get('amount_home', item.get('amount', 0.0))),
                        'amount_destination': float(item.get('amount_destination', 0.0)),
                        'date': item.get('date', ''),
                        'currency_home': item.get('currency_home', report_userconfig.get('home-currency', 'USD')),
                        'currency_destination': item.get('currency_destination', report_userconfig.get('destination-currency', 'USD')),
                        'expense_roe': item.get('expense_roe', report_userconfig.get('exchange-rate', '1.000000')),
                        'category': item.get('category', 'Other'),
                        'category_budget': item.get('category_budget', "0.00"),
                        'payment_method': item.get('payment_method', ''),
                        'location': item.get('location', ''),
                        'tags': item.get('tags', []),
                        'expense_goal': item.get('expense_goal', ''),
                        'support_partner': item.get('support_partner', '')
                    }

                    temp_expenses.append(expense_obj)
                except Exception as e:
                    print(f"Error processing expense item {i}: {e}")
                    continue

            report_expenses = temp_expenses
            print(f"Loaded {len(report_expenses)} expenses from localStorage")
        else:
            print("No expenses found in localStorage")
            report_expenses = []
    except Exception as e:
        print(f"Error loading expenses: {e}")
        report_expenses = []

def determine_expense_currency(expense):
    """Determine which currency to use for an expense based on amounts."""
    amount_home = expense.get('amount_home', 0.0)
    amount_destination = expense.get('amount_destination', 0.0)
    currency_home = expense.get('currency_home', report_userconfig.get('home-currency', 'USD'))
    currency_destination = expense.get('currency_destination', report_userconfig.get('destination-currency', 'USD'))

    # If destination amount exists and is greater than 0, use destination currency
    if amount_destination > 0:
        return currency_destination, amount_destination
    else:
        return currency_home, amount_home

def convert_to_home_currency(amount, currency, expense_roe=None):
    """Convert an amount to home currency equivalent."""
    home_currency = report_userconfig.get('home-currency', 'USD')

    # If already in home currency, return as is (rounded to 2 decimal places)
    if currency == home_currency:
        return round(amount, 2)

    # Use expense-specific exchange rate if available, otherwise use current rate
    exchange_rate = float(expense_roe) if expense_roe else float(report_userconfig.get('exchange-rate', '1.000000'))

    # Convert to home currency (multiply destination currency by exchange rate)
    # Round the final result to 2 decimal places for currency display
    converted_amount = amount * exchange_rate if exchange_rate != 0 else amount
    return round(converted_amount, 2)

def show_status(message, is_success=True):
    """Display status message."""
    status_area = document.getElementById("status_area")
    if status_area:
        status_class = "success" if is_success else "error"
        status_area.innerHTML = f'<div class="status-message {status_class}">{message}</div>'

def test_data_availability():
    """Test function to check if data is available."""
    print("=== DATA AVAILABILITY TEST ===")

    # Check localStorage directly
    stored_data = js.localStorage.getItem(EXPENSES_STORAGE_KEY)
    if stored_data:
        try:
            data = json.loads(stored_data)
            print(f"Found {len(data)} items in localStorage")
            if len(data) > 0:
                print(f"First item: {data[0]}")
        except Exception as e:
            print(f"Error parsing localStorage data: {e}")
    else:
        print("No data found in localStorage")

    # Check userconfig
    stored_config = js.localStorage.getItem(USER_CONFIG_KEY)
    if stored_config:
        try:
            config = json.loads(stored_config)
            print(f"Found userconfig: {config}")
        except Exception as e:
            print(f"Error parsing userconfig: {e}")
    else:
        print("No userconfig found in localStorage")

    print("=== END TEST ===")
    return stored_data is not None

# --- Main Report Generation Logic ---
async def generate_report():
    """Generates and displays the expense report."""
    print("Generating expense report...")

    # Test data availability first
    test_data_availability()

    # Load data
    await load_expense_categories()
    load_user_config()
    load_expenses()

    # Update home currency display
    home_currency_element = document.getElementById('home-currency-config')
    if home_currency_element:
        home_currency_element.textContent = report_userconfig.get('home-currency', 'USD')

    if not report_expenses:
        show_status("No expenses found to generate a report.", False)
        document.getElementById('currencies-count').textContent = "0"
        document.getElementById('categories-count').textContent = "0"
        document.getElementById('currencies-list').textContent = "None"
        document.getElementById('categories-list').textContent = "None"
        return

    print(f"Processing {len(report_expenses)} expenses for report generation...")

    # Process expenses and aggregate data
    # Structure: { currency: { category_group: { subcategory: total, '_total': category_total, '_home_currency_total': home_equivalent } } }
    report_data = {}
    grand_totals_by_currency = {}
    home_currency_totals_by_category = {}  # Track home currency equivalents by category group
    active_currencies = set()
    active_category_groups = set()

    for expense in report_expenses:
        try:
            # Determine currency and amount for this expense
            currency, amount = determine_expense_currency(expense)

            if amount <= 0:
                continue

            category = expense.get('category', 'Other')
            category_group = get_category_group(category)

            # Extract subcategory from full category (e.g., 'Food-Groceries' -> 'Groceries')
            if '-' in category:
                subcategory = category.split('-', 1)[1]
            else:
                subcategory = 'General'

            active_currencies.add(currency)
            active_category_groups.add(category_group)

            # Convert to home currency equivalent
            home_currency_amount = convert_to_home_currency(amount, currency, expense.get('expense_roe'))

            # Initialize data structures
            if currency not in report_data:
                report_data[currency] = {}
            if category_group not in report_data[currency]:
                report_data[currency][category_group] = {'_total': 0.0, '_home_currency_total': 0.0}
            if subcategory not in report_data[currency][category_group]:
                report_data[currency][category_group][subcategory] = 0.0

            # Add amounts (round currency amounts to 2 decimal places)
            report_data[currency][category_group][subcategory] += round(amount, 2)
            report_data[currency][category_group]['_total'] += round(amount, 2)
            report_data[currency][category_group]['_home_currency_total'] += home_currency_amount  # Already rounded in convert function

            # Track home currency totals by category group (across all currencies)
            if category_group not in home_currency_totals_by_category:
                home_currency_totals_by_category[category_group] = 0.0
            home_currency_totals_by_category[category_group] += home_currency_amount  # Already rounded

            # Add to grand totals
            if currency not in grand_totals_by_currency:
                grand_totals_by_currency[currency] = 0.0
            grand_totals_by_currency[currency] += round(amount, 2)

        except Exception as e:
            print(f"Error processing expense {expense.get('id', 'N/A')}: {e}")
            continue

    print(f"Found {len(active_currencies)} currencies and {len(active_category_groups)} category groups")

    # Update summary info
    currencies_count_element = document.getElementById('currencies-count')
    currencies_list_element = document.getElementById('currencies-list')
    categories_count_element = document.getElementById('categories-count')
    categories_list_element = document.getElementById('categories-list')

    if currencies_count_element:
        currencies_count_element.textContent = str(len(active_currencies))
    if currencies_list_element:
        currencies_list_element.textContent = ", ".join(sorted(active_currencies)) if active_currencies else "None"
    if categories_count_element:
        categories_count_element.textContent = str(len(active_category_groups))
    if categories_list_element:
        categories_list_element.textContent = ", ".join(sorted(active_category_groups)) if active_category_groups else "None"

    # Render report cards
    render_report_cards(report_data, home_currency_totals_by_category)

    # Render grand totals
    render_grand_totals(grand_totals_by_currency)

    show_status(f"Report generated successfully! Found {len(report_expenses)} expenses across {len(active_currencies)} currencies.", True)
    print("Report generation complete.")

def render_report_cards(report_data, home_currency_totals_by_category):
    """Render the expense report cards."""
    report_container = document.getElementById('report-container')
    if not report_container:
        print("Error: report-container not found")
        return

    report_container.innerHTML = ""  # Clear previous content

    if not report_data:
        report_container.innerHTML = '<div class="report-card"><h3>No Data Available</h3><p>No expenses found to generate report cards.</p></div>'
        return

    # Get home currency for display
    home_currency = report_userconfig.get('home-currency', 'USD')

    # Sort currencies for consistent output
    sorted_currencies = sorted(report_data.keys())

    cards_created = 0
    for currency in sorted_currencies:
        currency_data = report_data[currency]
        # Sort category groups for consistent output
        sorted_category_groups = sorted(currency_data.keys())

        for category_group in sorted_category_groups:
            category_data = currency_data[category_group]

            # Get budget amount for this category group
            budget_amount = float(report_userconfig.get('budgets', {}).get(category_group, '0.00'))

            # Create card HTML with budget information in header
            card_html = f'''
            <div class="report-card">
                <h3 class="category-budget-header">
                    <span class="category-header-title">{category_group} ({currency}) - </span>
                    <span class="category-header-budget"> Budget: {budget_amount:.2f} {home_currency}</span>
                </h3>

            '''

            # Get subcategories (excluding '_total' and '_home_currency_total')
            subcategory_items = []
            for subcat, amount in category_data.items():
                if subcat not in ['_total', '_home_currency_total'] and amount > 0:
                    subcategory_items.append((subcat, amount))

            # Sort subcategories alphabetically
            subcategory_items.sort(key=lambda x: x[0])

            if not subcategory_items:
                card_html += '<div class="subcategory-item"><span class="subcategory-label">No expenses recorded</span><span class="subcategory-value">-</span></div>'
            else:
                for subcat, amount in subcategory_items:
                    card_html += f'''
                    <div class="subcategory-item">
                        <span class="subcategory-label">{subcat}</span>
                        <span class="subcategory-value">{amount:.2f} {currency}</span>
                    </div>
                    '''

            # Add category total
            total_amount = category_data.get('_total', 0.0)
            card_html += f'''
                <div class="category-total">
                    <span>Total for {category_group}</span>
                    <span>{total_amount:.2f} {currency}</span>
                </div>
            '''

            # Add home currency equivalent if different from current currency
            home_currency_total = category_data.get('_home_currency_total', 0.0)
            if currency != home_currency and home_currency_total > 0:
                card_html += f'''
                    <div class="category-total" style="border-top: 1px solid #ddd; margin-top: 5px; padding-top: 5px;">
                        <span>Equivalent in {home_currency}</span>
                        <span>{home_currency_total:.2f} {home_currency}</span>
                    </div>
                '''

            card_html += '</div>'

            report_container.innerHTML += card_html
            cards_created += 1

    print(f"Generated {cards_created} report cards")

def render_grand_totals(grand_totals_by_currency):
    """Render the grand totals section."""
    grand_totals_container = document.getElementById('grand-totals-container')
    if not grand_totals_container:
        print("Error: grand-totals-container not found")
        return

    # Clear previous content but keep the header
    grand_totals_container.innerHTML = '<h3 class="section-subtitle">Grand Totals</h3>'

    if not grand_totals_by_currency:
        grand_totals_container.innerHTML += '<div class="grand-totals-item"><span class="grand-totals-currency">No expenses to total</span><span class="grand-totals-amount">-</span></div>'
    else:
        for currency, total_amount in sorted(grand_totals_by_currency.items()):
            grand_totals_container.innerHTML += f'''
            <div class="grand-totals-item">
                <span class="grand-totals-currency">Total spend in {currency} currency:</span>
                <span class="grand-totals-amount">{total_amount:.2f}</span>
            </div>
            '''

def setup_event_handlers():
    """Setup event handlers for the page."""
    refresh_btn = document.getElementById("refresh-report-btn")
    if refresh_btn:
        def refresh_handler(_):
            # Create a new task for the async function
            import asyncio
            asyncio.create_task(generate_report())

        refresh_btn.onclick = refresh_handler
        print("Refresh button event handler set up")

# --- Entry Point ---
async def main():
    """Main entry point for the report generation."""
    try:
        await generate_report()
        setup_event_handlers()
    except Exception as e:
        print(f"Error in main: {e}")
        show_status(f"Error generating report: {e}", False)

# Auto-execute when script loads
print("Starting expense report initialization...")
import asyncio
asyncio.create_task(main())