<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Configuration - Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css" />
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>
</head>
<body>
  <div class="app-container">
    <header class="header">
      <div class="container header-content">
        <div class="logo">ExpenseTracker</div>
        <div class="user-greeting">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="user-icon">
            <circle cx="12" cy="8" r="5"></circle>
            <path d="M20 21a8 8 0 1 0-16 0"></path>
          </svg>
          <span>Welcome Back, User!</span>
        </div>
      </div>
    </header>

    <main class="main-content">
      <div class="card">
        <h2 class="section-title">Currency and Budget Configuration</h2>
        <div id="status_area"></div>

        <h3 class="section-subtitle" style="margin-top: 2rem; margin-bottom: 1rem;">Currency Settings</h3>
        <p class="form-description" style="margin-bottom: 1rem;">Set Home Currency once, and adjust Destination Currency for each expense if needed.</p>

        <div class="form-container">
            <div class="form-row">
                <div class="form-group">
                    <label for="home_currency">Home Currency:</label>
                    <select id="home_currency" required>
                        <option value="">Select Home Currency</option>
                        <!-- Currency options will be populated dynamically -->
                    </select>
                    <div id="home_support_partner" class="support-partner-info"></div>
                </div>

                <div class="form-group">
                    <label for="destination_currency">Destination Currency:</label>
                    <select id="destination_currency">
                        <option value="">Select Destination Currency</option>
                        <!-- Currency options will be populated dynamically -->
                    </select>
                    <small class="form-description">Optional. Leave blank if you don't need a destination currency.</small>
                    <div id="destination_support_partner" class="support-partner-info"></div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="exchange_rate">Exchange Rate:</label>
                    <input type="number" id="exchange_rate" step="0.01" min="0.01" placeholder="e.g., 1.00">
                    <small class="form-description">Only required if Destination Currency is selected. Set current exchange rate between Home and Destination currencies using your own source of exchange rate and set the exchange rate so that Home Currency = 1.00</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group support-partner-container">
                    <h4>Regional Partners</h4>
                    <p class="form-description">The regional partner is automatically assigned based on the selected currency.</p>
                    <div class="support-partner-display">
                        <div class="support-partner-item">
                            <strong>Home Currency Regional Partner:</strong>
                            <span id="home_support_display">Not selected</span>
                        </div>
                        <div class="support-partner-item">
                            <strong>Destination Currency Regional Partner:</strong>
                            <span id="destination_support_display">Not selected</span>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="section-subtitle" style="margin-top: 2rem; margin-bottom: 1rem;">Budget Amounts</h3>
            <p class="form-description" style="margin-bottom: 1rem;">Set budget amounts for each expense category (Use Home Currency)</p>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_accommodation">Accommodation: (Hotels, Camping, Airbnb, Resort Fees)</label>
                    <input type="number" id="budget_accommodation" step="0.01" min="0" placeholder="e.g., 800.00">
                </div>

                <div class="form-group">
                    <label for="budget_education">Education: (Tuition, Books, Courses, Student Loans)</label>
                    <input type="number" id="budget_education" step="0.01" min="0" placeholder="e.g., 300.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_entertainment">Entertainment: (Movies, Concerts, Museums, Streaming Services, Books, Games, Hobbies)</label>
                    <input type="number" id="budget_entertainment" step="0.01" min="0" placeholder="e.g., 200.00">
                </div>

                <div class="form-group">
                    <label for="budget_financial">Financial: (Bank Fees, Loans, Taxes, Advisors)</label>
                    <input type="number" id="budget_financial" step="0.01" min="0" placeholder="e.g., 150.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_food">Food: (Groceries, Dining Out, Cafe, Takeaway, Snacks, Alcohol, Work Lunches)</label>
                    <input type="number" id="budget_food" step="0.01" min="0" placeholder="e.g., 600.00">
                </div>

                <div class="form-group">
                    <label for="budget_gifts">Gifts: (Presents, Charity Donations, Flowers)</label>
                    <input type="number" id="budget_gifts" step="0.01" min="0" placeholder="e.g., 100.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_health">Health: (Doctor, Dentist, Optician, Pharmacy, Hospital, Therapy, Gym, Sports, Vitamins, Insurance)</label>
                    <input type="number" id="budget_health" step="0.01" min="0" placeholder="e.g., 250.00">
                </div>

                <div class="form-group">
                    <label for="budget_home">Home: (Rent, Mortgage, Property Taxes, Home Insurance, Repairs, Furniture, Gardening, Cleaning, HOA Fees)</label>
                    <input type="number" id="budget_home" step="0.01" min="0" placeholder="e.g., 1200.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_kids">Kids: (Childcare, School Fees, School Supplies, Clothing, Activities, Allowance, Diapers, Baby Supplies)</label>
                    <input type="number" id="budget_kids" step="0.01" min="0" placeholder="e.g., 300.00">
                </div>

                <div class="form-group">
                    <label for="budget_personalcare">PersonalCare: (Haircut, Toiletries, Cosmetics, Spa, Dry Cleaning, Laundry)</label>
                    <input type="number" id="budget_personalcare" step="0.01" min="0" placeholder="e.g., 150.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_pets">Pets: (Food, Vet, Grooming, Toys, Boarding, Insurance)</label>
                    <input type="number" id="budget_pets" step="0.01" min="0" placeholder="e.g., 100.00">
                </div>

                <div class="form-group">
                    <label for="budget_shopping">Shopping: (Clothing, Shoes, Accessories, Electronics, Books, Home Goods, Sporting Goods, Software, Online Purchases)</label>
                    <input type="number" id="budget_shopping" step="0.01" min="0" placeholder="e.g., 400.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_transport">Transport: (Flights, Train, Bus, Taxi, Ferry, Rental Car, Fuel, Parking, Tolls, Public Transport Pass, Vehicle Maintenance, Vehicle Insurance, Vehicle Registration, Bicycle/Scooter Rental)</label>
                    <input type="number" id="budget_transport" step="0.01" min="0" placeholder="e.g., 300.00">
                </div>

                <div class="form-group">
                    <label for="budget_travel">Travel: (Activities, Souvenirs, Visas, Insurance, Foreign Currency Exchange, Luggage Fees, Airport Services, Tips/Gratuities)</label>
                    <input type="number" id="budget_travel" step="0.01" min="0" placeholder="e.g., 500.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_utilities">Utilities: (Electricity, Gas, Water, Internet, Mobile Phone, Landline Phone, Cable/Satellite TV, Trash/Recycling)</label>
                    <input type="number" id="budget_utilities" step="0.01" min="0" placeholder="e.g., 250.00">
                </div>

                <div class="form-group">
                    <label for="budget_work">Work: (Office Supplies, Software/Subscriptions, Professional Development, Client Meals/Entertainment, Business Travel, Membership Fees)</label>
                    <input type="number" id="budget_work" step="0.01" min="0" placeholder="e.g., 200.00">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="budget_other">Other: (Uncategorized, Cash Withdrawal, Legal Fees, Postage/Shipping, Miscellaneous)</label>
                    <input type="number" id="budget_other" step="0.01" min="0" placeholder="e.g., 100.00">
                </div>

                <div class="form-group">
                    <!-- Empty space for alignment -->
                </div>
            </div>

            <div class="form-actions">
                <button id="save_config" class="primary-button">Save Configuration</button>
            </div>
        </div>
      </div>
    </main>

    <nav class="bottom-nav">
      <div class="container nav-content">
        <a href="index.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          <span>Home</span>
        </a>
        <a href="add-expense.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="16"></line>
            <line x1="8" y1="12" x2="16" y2="12"></line>
          </svg>
          <span>Add Expense</span>
        </a>
        <a href="expense-cards.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="2" y="5" width="20" height="14" rx="2"></rect>
            <line x1="2" y1="10" x2="22" y2="10"></line>
          </svg>
          <span>All Expenses</span>
        </a>
        <a href="settings.html" class="nav-item">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
          <span>Settings</span>
        </a>
      </div>
    </nav>
  </div>

  <script type="py" src="./userconfig.py"></script>
</body>
</html>
